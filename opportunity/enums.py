from django.db import models

# 项目类型枚举
class ProjectTypeEnum(models.TextChoices):
    # 新梯项目
    NEW_LIFT_PROJECT = 'NEW_LIFT_PROJECT','新梯项目'
    # 旧梯改造
    OLD_LIFT_RENOVATION = 'OLD_LIFT_RENOVATION', '旧梯改造'
    # 更新项目
    UPDATE_PROJECT = 'UPDATE_PROJECT', '更新项目'
    

# 商务运营关系枚举
class BusinessOperationRelationEnum(models.TextChoices):
    # 切入业主 (Contacting owner)
    CONTACTING_OWNER = 'CONTACTING_OWNER', '切入业主'
    # 切入总包 (Contacting maincon)
    CONTACTING_MAINCON = 'CONTACTING_MAINCON', '切入总包'
    # 切入业主及总包 (Contacting owner&maincon)
    CONTACTING_OWNER_AND_MAINCON = 'CONTACTING_OWNER_AND_MAINCON', '切入业主及总包'
    # 拉拢业主 (Convince owner)
    CONVINCE_OWNER = 'CONVINCE_OWNER', '拉拢业主'
    # 拉拢总包 (Convince maincon)
    CONVINCE_MAINCON = 'CONVINCE_MAINCON', '拉拢总包'
    # 拉拢业主及总包 (Convince owner& maincon)
    CONVINCE_OWNER_AND_MAINCON = 'CONVINCE_OWNER_AND_MAINCON', '拉拢业主及总包'
    # 未运营 (Null)
    NULL = 'NULL', '未运营'
