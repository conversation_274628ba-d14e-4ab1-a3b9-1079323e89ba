from django.db import models

from core.enums import VietnamCityEnum, VietnamRegionEnum
from core.model import BaseModel
from opportunity.enums import BusinessOperationRelationEnum, ProjectTypeEnum
from staff.models import Staff


# 商机模型
class Opportunity(BaseModel):
    
    # - 基本信息
    # 项目编号
    number = models.CharField(max_length=255, verbose_name='项目编号', blank=True, default='')
    # 录入人
    input_user = models.ForeignKey(Staff, on_delete=models.SET_NULL, null=True, verbose_name='录入人')
    # 项目名称
    name = models.CharField(max_length=255, verbose_name='项目名称', blank=True, default='')
    # 项目类型
    type = models.CharField(max_length=255, verbose_name='项目类型', choices=ProjectTypeEnum.choices)
    # 预计签约日期
    expected_signing_date = models.DateField(verbose_name='预计签约日期')
    # 项目所在地
    location = models.CharField(max_length=255, verbose_name='项目所在地', choices=VietnamCityEnum.choices)
    # 项目地址
    address = models.CharField(max_length=255, verbose_name='项目地址', blank=True, default='')
    # 项目区域
    region = models.CharField(max_length=255, verbose_name='项目区域', choices=VietnamRegionEnum.choices)
    # 是否跨区
    is_cross_region = models.BooleanField(verbose_name='是否跨区')
    
    # - 商机成交几率
    # 商务关系运营
    business_relation = models.CharField(max_length=50,choices=BusinessOperationRelationEnum.choices,verbose_name='商务关系运营')
    # 竞争品牌
    competition_brand = models.CharField(max_length=255, verbose_name='竞争品牌', blank=True, default='')
