
from django.db import models
from rest_framework.exceptions import ValidationError
from rest_framework.validators import BaseValidator

# 基础模型
class BaseModel(models.Model):
    # 创建时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    # 更新时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        abstract = True
        

# 多选字段校验器
class JSONListValidator(BaseValidator):

    def __init__(self, allowed_choices, message=None):
        self.allowed_choices = [choice[0] for choice in allowed_choices]
        self.choices_display = dict(allowed_choices)
        super().__init__(limit_value=self.allowed_choices)
    
    def __call__(self, value):
        print(f"value: {value}")
        if not isinstance(value, list):
            raise ValidationError("数据校验失败")
        
        for item in value:
            if item not in self.allowed_choices:
                valid_options = [v for k, v in self.choices_display.items() if k != 'UNKNOWN']
                raise ValidationError(
                    f"无效值，允许的范围：{', '.join(valid_options)}"
                )